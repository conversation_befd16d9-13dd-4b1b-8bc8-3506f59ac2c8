{"host": "localhost", "username": "root", "password": "", "database": "telegram_scraper", "llm_timeout": 240, "server": {"host": "127.0.0.1", "port": 9096}, "openai": "https://api.openai.com/v1/chat/completions", "anthropic": "https://api.anthropic.com/v1/messages", "gemini": "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions", "xai": "https://api.x.ai/v1/chat/completions", "deepseek": "https://api.deepseek.com/chat/completions", "security": {"console_bearer_token": "abcd1234"}, "application": {"log_level": "INFO", "batch_size": 100, "historical_scraping_days": 90, "min_content_length": 2, "max_content_length": 10000, "debug_mode": false, "bypass_content_validation": false, "validation_thresholds": {"fake_content_confidence": 0.7, "trading_signal_confidence": 0.7, "roleplay_confidence": 0.7, "min_word_count": 2, "min_response_length": 10}}, "telegram": {"validate_config_on_startup": true, "test_api_connection": false, "retry_failed_posts": true}, "crypto": {"default_assets": ["BTC", "ETH", "SOL", "ADA", "DOT"], "analysis_depth": "detailed", "signal_confidence_threshold": 0.7, "sentiment_analysis": true, "trading_signal_generation": true, "system_prompt": "You are an expert content analyzer and summarizer. Your task is to analyze messages and provide concise, insightful summaries. Focus on key points, sentiment, and actionable information. Keep responses brief and to the point.", "user_prompt": "Analyze this message and provide a concise summary with key insights:\n\n{message_content}\n\nFormat: Brief summary (1-2 sentences) + Key points (bullet points) + Sentiment (positive/negative/neutral) + Actionable insights (if any)", "crypto_system_prompt": "You are a cryptocurrency market analyst. Analyze crypto-related messages and provide a single comprehensive trading signal summary. Focus on the most significant signal in the message. Be concise and focus on actionable information. Format: Token mentioned + Sentiment + Key insight + Trading signal (if clear) + Confidence level (1-10)", "crypto_user_prompt": "Analyze this crypto message and provide ONE consolidated trading signal post ONLY. Do not list multiple signals. Focus on the most significant signal in the message:\n\n{message_content}\n\nFormat: Token: [name] | Sentiment: [bullish/bearish/neutral] | Insight: [1 sentence] | Signal: [buy/sell/hold] | Confidence: [1-10]/10", "content_generation": {"default_type": "article", "max_length": 2000, "style_guides": {"article": {"tone": "informative", "structure": "introduction, body, conclusion"}, "social_post": {"tone": "engaging", "max_length": 280}, "report": {"tone": "professional", "structure": "executive_summary, findings, recommendations"}}}}}